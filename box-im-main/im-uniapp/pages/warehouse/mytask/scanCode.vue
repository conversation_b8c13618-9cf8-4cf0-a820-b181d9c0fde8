<template>
  <view class="page scan-code">
    <nav-bar back>扫码入库</nav-bar>

    <view class="content">
      <!-- 参数信息显示 -->
      <view class="info-section">
        <view class="info-card">
          <view class="info-title">入库信息</view>
          <view class="info-item">
            <text class="info-label">工单编号：</text>
            <text class="info-value">{{ orderCode }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">数量：</text>
            <text class="info-value">{{ quantity }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">板型：</text>
            <text class="info-value">{{ boardType }}</text>
          </view>
        </view>
      </view>

      <!-- 选择入库类型 -->
      <view class="selection-section">
        <view class="section-title">选择入库类型</view>
        <view class="selection-buttons">
          <view
            v-for="option in storageOptions"
            :key="option.value"
            class="selection-btn"
            :class="{ 'active': selectedStorageType === option.value }"
            @click="selectStorageType(option.value)"
          >
            <text class="btn-text">{{ option.label }}</text>
          </view>
        </view>
      </view>

      <!-- 扫码按钮 -->
      <view class="scan-section">
        <view
          class="scan-btn"
          :class="{ 'disabled': !selectedStorageType }"
          @click="startScan"
        >
          <view class="scan-icon">📷</view>
          <text class="scan-text">扫描二维码</text>
        </view>
        <text class="scan-tip">请选择入库类型后进行扫码</text>
      </view>

      <!-- 扫码结果显示 -->
      <view v-if="scanResult" class="result-section">
        <view class="result-card">
          <view class="result-title">扫码结果</view>
          <view class="result-item">
            <text class="result-label">原始数据：</text>
            <text class="result-value">{{ scanResult.raw }}</text>
          </view>
          <view v-if="scanResult.purchaseNo" class="result-item">
            <text class="result-label">采购单号：</text>
            <text class="result-value">{{ scanResult.purchaseNo }}</text>
          </view>
        </view>
      </view>

      <!-- 确认入库按钮 -->
      <view v-if="scanResult && scanResult.purchaseNo" class="confirm-section">
        <view
          class="confirm-btn"
          :class="{ 'loading': isSubmitting }"
          @click="confirmInbound"
        >
          <text v-if="isSubmitting" class="loading-text">入库中...</text>
          <text v-else class="confirm-text">确认入库</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { inboundSemiFinishedProduct, showMessage } from '@/api/workOrders.js'

export default {
  name: "scanCode",
  data() {
    return {
      // 页面参数
      orderCode: '',
      stepTaskId: '',
      quantity: 0,
      boardType: '',
      itemName: '',

      // 入库类型选项
      storageOptions: [],
      selectedStorageType: '',

      // 扫码相关
      scanResult: null,

      // 提交状态
      isSubmitting: false
    }
  },

  onLoad(options) {
    // 获取页面参数
    this.orderCode = options.orderCode || ''
    this.stepTaskId = options.stepTaskId || ''
    this.quantity = parseInt(options.quantity) || 0
    this.boardType = options.boardType || ''
    this.itemName = options.itemName || ''

    console.log('scanCode页面参数:', {
      orderCode: this.orderCode,
      stepTaskId: this.stepTaskId,
      quantity: this.quantity,
      boardType: this.boardType,
      itemName: this.itemName
    })

    // 根据boardType设置入库选项
    this.setupStorageOptions()
  },

  methods: {
    /** 根据boardType设置入库选项 */
    setupStorageOptions() {
      if (this.boardType === '上下板') {
        this.storageOptions = [
          { label: '上板入库', value: '上板入库' },
          { label: '下板入库', value: '下板入库' }
        ]
      } else if (this.boardType === '单板') {
        this.storageOptions = [
          { label: '单板入库', value: '单板入库' }
        ]
      } else {
        // 默认情况，显示所有选项
        this.storageOptions = [
          { label: '上板入库', value: '上板入库' },
          { label: '下板入库', value: '下板入库' },
          { label: '单板入库', value: '单板入库' }
        ]
      }

      console.log('设置入库选项:', this.storageOptions)
    },

    /** 选择入库类型 */
    selectStorageType(type) {
      this.selectedStorageType = type
      console.log('选择入库类型:', type)
    },

    /** 开始扫码 */
    startScan() {
      if (!this.selectedStorageType) {
        showMessage('请先选择入库类型', 'error')
        return
      }

      // 调用uni-app扫码API
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
        success: (res) => {
          console.log('扫码成功:', res.result)
          this.processScanResult(res.result)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          showMessage('扫码失败，请重试', 'error')
        }
      })
    },

    /** 处理扫码结果 */
    processScanResult(scanData) {
      try {
        console.log('处理扫码结果:', scanData)

        // 解析二维码内容，提取purchase_no
        const purchaseNo = this.extractPurchaseNo(scanData)

        this.scanResult = {
          raw: scanData,
          purchaseNo: purchaseNo
        }

        if (!purchaseNo) {
          showMessage('无法解析采购单号，请检查二维码格式', 'error')
        } else {
          showMessage('扫码成功，请确认入库信息', 'success')
        }

      } catch (error) {
        console.error('处理扫码结果失败:', error)
        showMessage('处理扫码结果失败: ' + (error.message || '未知错误'), 'error')
      }
    },

    /** 提取采购单号 */
    extractPurchaseNo(scanData) {
      try {
        console.log('开始解析purchase_no，扫码数据:', scanData)

        // 尝试解析JSON格式
        if (scanData.startsWith('{') && scanData.endsWith('}')) {
          const jsonData = JSON.parse(scanData)
          console.log('解析JSON数据:', jsonData)

          // 按优先级提取purchase_no
          const purchaseNo = jsonData.purchase_no ||
                           jsonData.purchaseNo ||
                           jsonData.purchase_order_no ||
                           jsonData.code

          if (purchaseNo) {
            console.log('从JSON中提取到purchase_no:', purchaseNo)
            return purchaseNo
          }
        }

        // 如果不是JSON格式，检查是否直接是采购单号
        if (scanData.startsWith('PO') && scanData.length > 5) {
          console.log('直接使用扫码数据作为purchase_no:', scanData)
          return scanData
        }

        console.warn('无法从扫码数据中提取purchase_no')
        return null

      } catch (error) {
        console.error('解析purchase_no失败:', error)
        return null
      }
    },

    /** 确认入库 */
    async confirmInbound() {
      if (this.isSubmitting) {
        return
      }

      // 验证必要参数
      if (!this.scanResult || !this.scanResult.purchaseNo) {
        showMessage('请先扫码获取采购单号', 'error')
        return
      }

      if (!this.selectedStorageType) {
        showMessage('请选择入库类型', 'error')
        return
      }

      if (!this.stepTaskId) {
        showMessage('工序任务ID不存在', 'error')
        return
      }

      try {
        this.isSubmitting = true

        // 显示加载提示
        uni.showLoading({
          title: '入库中...'
        })

        // 准备API参数
        const formData = {
          quantity: this.quantity,                    // 数量
          purchaseNo: this.scanResult.purchaseNo,     // 从二维码解析的 purchase_no 值
          itemName: this.itemName || '未知物料',       // 物料名称
          stepTaskId: this.stepTaskId,               // 步骤任务ID
          boardType: this.selectedStorageType        // 用户在选择框中选择的入库类型
        }

        console.log('调用inboundSemiFinishedProduct API，参数:', formData)

        // 调用入库API
        const response = await inboundSemiFinishedProduct(formData)

        console.log('入库API响应:', response)

        uni.hideLoading()

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          // 显示成功提示
          uni.showToast({
            title: '入库成功',
            icon: 'success',
            duration: 2000
          })

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)

        } else {
          console.error('入库失败详情:', {
            code: response?.code,
            message: response?.message,
            data: response?.data
          })

          // 显示后端返回的具体错误原因
          const errorMessage = response?.message || response?.msg || '入库失败，请稍后重试'
          showMessage(errorMessage, 'error')
        }

      } catch (error) {
        console.error('入库操作失败:', error)
        uni.hideLoading()
        showMessage('入库操作失败: ' + (error.message || '网络错误'), 'error')
      } finally {
        this.isSubmitting = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20rpx;
}

/* 信息卡片 */
.info-section {
  margin-bottom: 30rpx;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 选择区域 */
.selection-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.selection-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.selection-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  background: white;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.active {
    background: #007aff;
    border-color: #007aff;

    .btn-text {
      color: white;
    }
  }

  &:active {
    transform: scale(0.98);
  }
}

.btn-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 扫码区域 */
.scan-section {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scan-btn {
  width: 300rpx;
  height: 300rpx;
  background: linear-gradient(135deg, #007aff, #5ac8fa);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;

  &.disabled {
    background: #ccc;
    box-shadow: none;
  }

  &:active:not(.disabled) {
    transform: scale(0.95);
  }
}

.scan-icon {
  font-size: 80rpx;
  margin-bottom: 10rpx;
}

.scan-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

.scan-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  text-align: center;
}

/* 结果显示 */
.result-section {
  margin-bottom: 30rpx;
}

.result-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-item {
  display: flex;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.result-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.result-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

/* 确认按钮 */
.confirm-section {
  margin-bottom: 30rpx;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #28a745, #20c997);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;

  &.loading {
    background: #ccc;
    box-shadow: none;
  }

  &:active:not(.loading) {
    transform: scale(0.98);
  }
}

.confirm-text,
.loading-text {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}
</style>